<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Your Job Post - GigGenius</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/page1-2025.css') }}">
</head>
<body>
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        <i class="fas fa-info-circle"></i>
                        {{ message }}
                        <button type="button" class="alert-close" onclick="this.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <form action="/save_page1" method="POST" id="jobTypeForm">
        <div class="container">
            <!-- Progress Indicator -->
            <div class="progress-indicator">
                <div class="progress-step">
                    <div class="step-number active">1</div>
                    <div class="step-label active">Job Type</div>
                </div>
                <div class="step-connector"></div>
                <div class="progress-step">
                    <div class="step-number">2</div>
                    <div class="step-label">Job Details</div>
                </div>
                <div class="step-connector"></div>
                <div class="progress-step">
                    <div class="step-number">3</div>
                    <div class="step-label">Review & Post</div>
                </div>
            </div>

            <!-- Header Section -->
            <div class="header-section">
                <div class="title-section">
                    <h1>Let's create your perfect job post</h1>
                    <p>Choose the option that best fits your needs and we'll guide you through the process</p>
                </div>

                <!-- Quick Stats -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <i class="fas fa-users"></i>
                        <span>10,000+ Talented Professionals</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>Average Response: 2 Hours</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-star"></i>
                        <span>4.9/5 Average Rating</span>
                    </div>
                </div>
            </div>
            <!-- Main Content Section -->
            <div class="main-content">
                <!-- Primary Options -->
                <div class="options-section">
                    <h2 class="section-title">
                        <i class="fas fa-plus-circle"></i>
                        What would you like to do?
                    </h2>

                    <!-- Create New Job -->
                    <div class="primary-option-card" id="newJobCard">
                        <div class="option-header">
                            <div class="option-icon">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="option-content">
                                <h3>Create a New Job Post</h3>
                                <p>Start fresh with a brand new job posting</p>
                            </div>
                            <div class="option-toggle">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>

                        <div class="option-details" id="newJobDetails">
                            <div class="job-type-selection">
                                <div class="job-type-card" onclick="selectJobType('one-time', this)">
                                    <input type="radio" name="job_type" value="one-time" hidden>
                                    <div class="job-type-icon">
                                        <i class="fas fa-calendar-day"></i>
                                    </div>
                                    <div class="job-type-info">
                                        <h4>One-time Project</h4>
                                        <p>Single project with defined scope and timeline</p>
                                        <div class="job-type-features">
                                            <span><i class="fas fa-check"></i> Fixed scope</span>
                                            <span><i class="fas fa-bullseye"></i> Clear deliverables</span>
                                            <span><i class="fas fa-calendar-check"></i> Set deadline</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="job-type-card" onclick="selectJobType('ongoing', this)">
                                    <input type="radio" name="job_type" value="ongoing" hidden>
                                    <div class="job-type-icon">
                                        <i class="fas fa-sync"></i>
                                    </div>
                                    <div class="job-type-info">
                                        <h4>Ongoing Collaboration</h4>
                                        <p>Long-term partnership with flexible schedule</p>
                                        <div class="job-type-features">
                                            <span><i class="fas fa-check"></i> Flexible hours</span>
                                            <span><i class="fas fa-handshake"></i> Long-term</span>
                                            <span><i class="fas fa-clock"></i> Hourly/retainer</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Continue Draft -->
                    {% if draft_jobs %}
                    <div class="primary-option-card" id="draftCard">
                        <div class="option-header">
                            <div class="option-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="option-content">
                                <h3>Continue Draft</h3>
                                <p>Resume working on a saved draft ({{ draft_jobs|length }} available)</p>
                            </div>
                            <div class="option-toggle">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>

                        <div class="option-details" id="draftDetails">
                            <div class="draft-list">
                                {% for draft in draft_jobs %}
                                <div class="draft-item" onclick="selectDraft(this, '{{ draft.title }}', {{ draft.id }})">
                                    <div class="draft-info">
                                        <h4>{{ draft.title }}</h4>
                                        <p class="draft-date">
                                            <i class="fas fa-calendar"></i>
                                            Created {{ draft.created_at.strftime('%b %d, %Y') if draft.created_at else 'Recently' }}
                                        </p>
                                    </div>
                                    <div class="draft-action">
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Rework Previous Job -->
                    <div class="primary-option-card" id="reworkCard">
                        <div class="option-header">
                            <div class="option-icon">
                                <i class="fas fa-redo"></i>
                            </div>
                            <div class="option-content">
                                <h3>Rework a Previous Job</h3>
                                <p>Repost or modify a paused or completed job posting</p>
                            </div>
                            <div class="option-toggle">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>

                        <div class="option-details" id="reworkDetails">
                            <div class="rework-list">
                                <!-- This will be populated with paused/completed jobs from the database -->
                                <div class="rework-item" onclick="selectRework(this, 'Web Developer Position', 'paused', 123)">
                                    <div class="rework-info">
                                        <h4>Web Developer Position</h4>
                                        <p class="rework-status">
                                            <i class="fas fa-pause-circle"></i>
                                            Status: Paused • Posted 2 weeks ago
                                        </p>
                                        <p class="rework-description">Full-stack developer needed for e-commerce project</p>
                                    </div>
                                    <div class="rework-action">
                                        <span class="rework-badge paused">Paused</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>

                                <div class="rework-item" onclick="selectRework(this, 'UI/UX Designer', 'completed', 124)">
                                    <div class="rework-info">
                                        <h4>UI/UX Designer</h4>
                                        <p class="rework-status">
                                            <i class="fas fa-check-circle"></i>
                                            Status: Completed • Posted 1 month ago
                                        </p>
                                        <p class="rework-description">Mobile app design for iOS and Android</p>
                                    </div>
                                    <div class="rework-action">
                                        <span class="rework-badge completed">Completed</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>

                                <div class="rework-item" onclick="selectRework(this, 'Content Writer', 'paused', 125)">
                                    <div class="rework-info">
                                        <h4>Content Writer</h4>
                                        <p class="rework-status">
                                            <i class="fas fa-pause-circle"></i>
                                            Status: Paused • Posted 5 days ago
                                        </p>
                                        <p class="rework-description">Blog posts and website content creation</p>
                                    </div>
                                    <div class="rework-action">
                                        <span class="rework-badge paused">Paused</span>
                                        <i class="fas fa-arrow-right"></i>
                                    </div>
                                </div>

                                <!-- If no paused/completed jobs -->
                                <div class="no-rework-jobs" style="display: none;">
                                    <i class="fas fa-info-circle"></i>
                                    <p>No paused or completed jobs available to rework.</p>
                                    <small>Jobs that are paused or completed will appear here for easy reposting.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
            <!-- Action Buttons -->
            <div class="action-section">
                <div class="action-buttons">
                    <button type="button" class="secondary-button" onclick="cancelProcess()">
                        <i class="fas fa-times"></i>
                        Cancel
                    </button>
                    <button type="button" class="primary-button disabled" id="continueButton" onclick="handleContinue()">
                        <span class="button-text">Continue</span>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>

                <!-- Selection Summary -->
                <div class="selection-summary" id="selectionSummary" style="display: none;">
                    <div class="summary-content">
                        <i class="fas fa-check-circle"></i>
                        <span id="summaryText">Ready to continue</span>
                    </div>
                </div>
            </div>

            <!-- Hidden inputs -->
            <input type="hidden" id="selectedDraftId" name="draft_job_id" value="">
            <input type="hidden" id="selectedMode" name="mode" value="">
            <input type="hidden" id="selectedRework" name="rework_job_id" value="">

            <!-- Fallback for browsers with JavaScript disabled -->
            <noscript>
                <div class="noscript-fallback">
                    <div class="noscript-content">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>JavaScript Required</h3>
                        <p>Please enable JavaScript for the best experience with GigGenius.</p>
                        <button type="submit" class="noscript-button">
                            Continue Anyway
                        </button>
                    </div>
                </div>
            </noscript>
        </div>
    </form>





    <script>
        // Global variables
        let selectedOption = null;
        let selectedJobType = null;
        let selectedDraftId = null;
        let selectedReworkId = null;

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the page
            initializePage();

            // Add event listeners
            setupEventListeners();

            // Animate progress steps
            animateProgressSteps();

            // Auto-expand first option for better UX
            setTimeout(() => {
                toggleOptionCard('newJobCard');
            }, 500);
        });

        function initializePage() {
            // Clear any previous selections
            clearLocalStorage();

            // Ensure continue button is initially disabled
            const continueButton = document.getElementById('continueButton');
            if (continueButton) {
                continueButton.classList.add('disabled');
            }
        }

        function setupEventListeners() {
            // Add click listeners to option cards
            document.querySelectorAll('.primary-option-card').forEach(card => {
                const header = card.querySelector('.option-header');
                if (header) {
                    header.addEventListener('click', function() {
                        toggleOptionCard(card.id);
                    });
                }
            });

            // Close option cards when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.primary-option-card') && !event.target.closest('.help-modal')) {
                    // Don't close if clicking on help modal
                }
            });
        }

        function animateProgressSteps() {
            const steps = document.querySelectorAll('.step-number');
            steps.forEach((step, index) => {
                setTimeout(() => {
                    step.classList.add('animate-pulse');
                    setTimeout(() => step.classList.remove('animate-pulse'), 600);
                }, index * 200);
            });
        }

        function clearLocalStorage() {
            // Clear previous selections
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith('editedCategory_') || key.startsWith('editedTitle_') ||
                    key.startsWith('selected') || key.startsWith('project')) {
                    localStorage.removeItem(key);
                }
            });
        }

        function toggleOptionCard(cardId) {
            const card = document.getElementById(cardId);
            if (!card) return;

            const details = card.querySelector('.option-details');
            const toggle = card.querySelector('.option-toggle i');

            // Close other cards
            document.querySelectorAll('.primary-option-card').forEach(otherCard => {
                if (otherCard.id !== cardId) {
                    const otherDetails = otherCard.querySelector('.option-details');
                    const otherToggle = otherCard.querySelector('.option-toggle i');
                    if (otherDetails) {
                        otherDetails.style.display = 'none';
                        otherCard.classList.remove('expanded');
                    }
                    if (otherToggle) {
                        otherToggle.style.transform = 'rotate(0deg)';
                    }
                }
            });

            // Toggle current card
            if (details.style.display === 'none' || !details.style.display) {
                details.style.display = 'block';
                card.classList.add('expanded');
                toggle.style.transform = 'rotate(180deg)';

                // Animate the expansion
                setTimeout(() => {
                    details.style.opacity = '1';
                    details.style.transform = 'translateY(0)';
                }, 10);
            } else {
                details.style.opacity = '0';
                details.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    details.style.display = 'none';
                    card.classList.remove('expanded');
                }, 300);
                toggle.style.transform = 'rotate(0deg)';
            }
        }

        function selectJobType(jobType, element) {
            selectedJobType = jobType;
            selectedOption = 'new';

            // Clear other selections
            selectedDraftId = null;
            selectedReworkId = null;

            // Update UI
            document.querySelectorAll('.job-type-card').forEach(card => {
                card.classList.remove('selected');
            });
            element.classList.add('selected');

            // Update hidden input
            const input = element.querySelector('input[type="radio"]');
            if (input) {
                input.checked = true;
            }

            // Store in localStorage
            localStorage.setItem('selected_job_type', jobType);
            localStorage.setItem('mode', 'create');

            // Enable continue button
            enableContinueButton(`Selected: ${jobType === 'one-time' ? 'One-time Project' : 'Ongoing Collaboration'}`);

            console.log('Selected job type:', jobType);
        }

        function selectDraft(element, title, draftId) {
            selectedOption = 'draft';
            selectedDraftId = draftId;

            // Clear other selections
            selectedJobType = null;
            selectedReworkId = null;

            // Update UI
            document.querySelectorAll('.draft-item').forEach(item => {
                item.classList.remove('selected');
            });
            element.classList.add('selected');

            // Store in localStorage
            localStorage.setItem('selectedJobTitle', title);
            localStorage.setItem('selectedDraftId', draftId);
            localStorage.setItem('mode', 'draft');

            // Update hidden input
            document.getElementById('selectedDraftId').value = draftId;

            // Enable continue button
            enableContinueButton(`Selected Draft: ${title}`);

            console.log('Selected draft:', title, 'ID:', draftId);
        }

        function selectRework(element, jobTitle, status, jobId) {
            selectedOption = 'rework';
            selectedReworkId = jobId;

            // Clear other selections
            selectedJobType = null;
            selectedDraftId = null;

            // Update UI
            document.querySelectorAll('.rework-item').forEach(item => {
                item.classList.remove('selected');
            });
            element.classList.add('selected');

            // Store in localStorage
            localStorage.setItem('selectedReworkJob', jobTitle);
            localStorage.setItem('selectedReworkId', jobId);
            localStorage.setItem('selectedReworkStatus', status);
            localStorage.setItem('mode', 'rework');

            // Update hidden input
            document.getElementById('selectedRework').value = jobId;

            // Enable continue button
            const statusText = status === 'paused' ? 'Paused' : 'Completed';
            enableContinueButton(`Selected ${statusText} Job: ${jobTitle}`);

            console.log('Selected rework job:', jobTitle, 'Status:', status, 'ID:', jobId);
        }

        function enableContinueButton(summaryText) {
            const continueButton = document.getElementById('continueButton');
            const selectionSummary = document.getElementById('selectionSummary');
            const summaryTextElement = document.getElementById('summaryText');

            // Enable button
            continueButton.classList.remove('disabled');
            continueButton.classList.add('enabled');

            // Show summary
            if (selectionSummary && summaryTextElement) {
                summaryTextElement.textContent = summaryText;
                selectionSummary.style.display = 'block';

                // Animate summary appearance
                setTimeout(() => {
                    selectionSummary.style.opacity = '1';
                    selectionSummary.style.transform = 'translateY(0)';
                }, 10);
            }

            // Add pulse animation to button
            continueButton.classList.add('pulse-animation');
            setTimeout(() => {
                continueButton.classList.remove('pulse-animation');
            }, 600);
        }

        function handleContinue() {
            if (!selectedOption) {
                showNotification('Please select an option to continue', 'warning');
                return;
            }

            // Show loading state
            showLoadingState();

            // Handle different selection types
            if (selectedOption === 'draft' && selectedDraftId) {
                // Redirect to page3 for draft editing
                setTimeout(() => {
                    window.location.href = `/page3?draft_job_id=${selectedDraftId}`;
                }, 1000);
            } else if (selectedOption === 'new' && selectedJobType) {
                // Store job type and redirect to page2
                localStorage.setItem('selected_job_type', selectedJobType);
                setTimeout(() => {
                    window.location.href = '/page2';
                }, 1000);
            } else if (selectedOption === 'rework' && selectedReworkId) {
                // Store rework job info and redirect to page2
                localStorage.setItem('selectedReworkId', selectedReworkId);
                localStorage.setItem('mode', 'rework');
                setTimeout(() => {
                    window.location.href = '/page2';
                }, 1000);
            } else {
                hideLoadingState();
                showNotification('Please make a selection to continue', 'error');
            }
        }

        function showLoadingState() {
            const continueButton = document.getElementById('continueButton');
            const buttonText = continueButton.querySelector('.button-text');
            const buttonIcon = continueButton.querySelector('i');

            continueButton.classList.add('loading');
            buttonText.textContent = 'Processing...';
            buttonIcon.className = 'fas fa-spinner fa-spin';
        }

        function hideLoadingState() {
            const continueButton = document.getElementById('continueButton');
            const buttonText = continueButton.querySelector('.button-text');
            const buttonIcon = continueButton.querySelector('i');

            continueButton.classList.remove('loading');
            buttonText.textContent = 'Continue';
            buttonIcon.className = 'fas fa-arrow-right';
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()"><i class="fas fa-times"></i></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        function cancelProcess() {
            if (confirm('Are you sure you want to cancel? Any unsaved progress will be lost.')) {
                window.location.href = '/client_page';
            }
        }


    </script>
</body>
</html>
